# Crypto Asset Intelligence Dashboard – Project Requirements

## Overview
We want to build (or leverage an existing open-source project for) a **crypto intelligence dashboard** that allows us to drill down from **blockchains → assets → detailed asset information**.

The system should have both **backend + frontend**, ideally modular, so we can extend it later with AI-powered filtering (e.g., DeepSeek API).

---

## Core User Flow

1. **Homepage (Blockchain List)**
   - Display a list of blockchains (e.g., Bitcoin, Ethereum, Polygon, Solana, etc.).
   - Each blockchain is clickable.

2. **Blockchain Page (Top Assets)**
   - When clicking a blockchain, show **top 100 traded assets**.
   - Each asset should display trading stats with timeframes:
     - 1m, 5m, 1h, 1d, 1w, 1m, 1y.
   - Assets should be clickable.

3. **Asset Page (Detailed Tabs)**
   - Page header: **Asset Name / Symbol**.
   - Below header: **Tabbed interface** with the following sections:

     ### a) News
     - Top 10 articles about this asset (API or scraping).
     - Ideally filterable (later we will add AI-powered filtering).

     ### b) Trades
     - **10 highest trades** (amount, date, time, region).
     - **10 lowest trades** (amount, date, time, region).

     ### c) Wallet Analysis
     - Top 10 owners wallets (by holdings).
     - Top 10 most trading volume by wallets.

     ### d) Trading Patterns
     - Frequency of trading.
     - Time of day trades.
     - Region of trades by activity.

---

## Technical Notes

- **Backend**:
  - Python (FastAPI or Django REST recommended).
  - Fetch market/trade data from public APIs (CoinGecko, CoinMarketCap, DefiLlama, CryptoCompare, Dune, etc.).
  - Store results in a database (PostgreSQL or MongoDB).

- **Frontend**:
  - React (Next.js preferred) or Streamlit for fast prototyping.
  - Clean, tabbed UI with drill-down navigation.
  - Data visualization with charts (Recharts, D3.js, or Plotly).

- **Data Sources**:
  - Market data: CoinGecko API (free), DefiLlama API, CryptoCompare.
  - News: NewsCatcher API, CryptoPanic API, or RSS + Feedparser.
  - Wallet/trade analysis: Etherscan, Blockchain.com explorers, Dune Analytics queries.

- **Future Add-On**:
  - DeepSeek AI integration to filter hype/fake/noise news.

---

## Open Questions for Dev Team

1. Is there an **existing open-source project** we can fork to save development time? (Cedix, Fundus, etc., but may need a better frontend).
2. If no suitable project exists, can we build this **entirely free** using open APIs?
3. What’s the best tech stack to allow **modularity + scalability** (e.g., plug in DeepSeek later)?

---

## Deliverables

- **MVP (Phase 1)**:
  - Blockchain list → asset list → asset detail tabs.
  - Basic market + news + wallet data integrated.
  - Simple but clean UI.

- **Phase 2**:
  - AI filtering (DeepSeek).
  - More advanced analytics (regional trading heatmaps, sentiment analysis).
  - User accounts + watchlists.

---
